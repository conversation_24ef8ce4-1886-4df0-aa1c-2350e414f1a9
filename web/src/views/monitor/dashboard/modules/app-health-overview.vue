<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { NCard, NGrid, NGridItem, NStatistic, NTag, NSpin, NAlert } from 'naive-ui';
import { fetchAppHealth } from '@/service/api';

// 应用健康状态数据
const loading = ref(true);
const error = ref<string | null>(null);
const healthData = ref<Api.Monitor.AppHealthData | null>(null);

onMounted(async () => {
  await loadHealthData();
});

async function loadHealthData() {
  try {
    loading.value = true;
    error.value = null;

    // 调用监控API获取应用健康状态
    const { data } = await fetchAppHealth();
    healthData.value = data;
  } catch (err) {
    error.value = err instanceof Error ? err.message : '加载健康数据失败';
    console.error('加载应用健康数据失败:', err);
  } finally {
    loading.value = false;
  }
}

// 暴露方法供父组件调用
defineExpose({
  loadHealthData
});

function getStatusColor(status: string) {
  switch (status) {
    case 'healthy':
      return 'success';
    case 'warning':
      return 'warning';
    case 'error':
      return 'error';
    default:
      return 'default';
  }
}

function formatUptime(seconds: number) {
  const days = Math.floor(seconds / 86400);
  const hours = Math.floor((seconds % 86400) / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  return `${days}天 ${hours}小时 ${minutes}分钟`;
}

function formatBytes(bytes: number) {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}
</script>

<template>
  <NCard title="应用健康状态" class="h-full">
    <NSpin :show="loading">
      <div v-if="error" class="mb-4">
        <NAlert type="error" :title="error" closable @close="error = null" />
      </div>

      <div v-if="healthData" class="space-y-6">
        <!-- 应用基本信息 -->
        <div>
          <h3 class="text-lg font-semibold mb-4">应用信息</h3>
          <NGrid :cols="4" :x-gap="16" :y-gap="16">
            <NGridItem>
              <NStatistic label="应用名称" :value="healthData.app_info.name" />
            </NGridItem>
            <NGridItem>
              <NStatistic label="版本" :value="healthData.app_info.version" />
            </NGridItem>
            <NGridItem>
              <NStatistic label="运行时长" :value="formatUptime(healthData.app_info.uptime)" />
            </NGridItem>
            <NGridItem>
              <div class="flex items-center space-x-2">
                <span class="text-sm text-gray-600">状态:</span>
                <NTag :type="getStatusColor(healthData.app_info.status)">
                  {{ healthData.app_info.status === 'healthy' ? '健康' :
                     healthData.app_info.status === 'warning' ? '警告' : '错误' }}
                </NTag>
              </div>
            </NGridItem>
          </NGrid>
        </div>

        <!-- 组件状态 -->
        <div>
          <h3 class="text-lg font-semibold mb-4">组件状态</h3>
          <NGrid :cols="3" :x-gap="16" :y-gap="16">
            <!-- 数据库状态 -->
            <NGridItem>
              <NCard size="small" title="数据库">
                <div class="space-y-2">
                  <div class="flex justify-between">
                    <span>状态:</span>
                    <NTag :type="getStatusColor(healthData.database.status === 'connected' ? 'healthy' : 'error')">
                      {{ healthData.database.status === 'connected' ? '已连接' : '断开' }}
                    </NTag>
                  </div>
                  <div class="flex justify-between">
                    <span>大小:</span>
                    <span>{{ formatBytes(healthData.database.size) }}</span>
                  </div>
                  <div class="flex justify-between">
                    <span>连接池:</span>
                    <span>{{ healthData.database.connection_pool.active }}/{{ healthData.database.connection_pool.total }}</span>
                  </div>
                </div>
              </NCard>
            </NGridItem>

            <!-- STRM任务状态 -->
            <NGridItem>
              <NCard size="small" title="STRM任务">
                <div class="space-y-2">
                  <div class="flex justify-between">
                    <span>状态:</span>
                    <NTag :type="getStatusColor(healthData.strm_tasks.status)">
                      {{ healthData.strm_tasks.status === 'healthy' ? '健康' :
                         healthData.strm_tasks.status === 'warning' ? '警告' : '错误' }}
                    </NTag>
                  </div>
                  <div class="flex justify-between">
                    <span>总任务:</span>
                    <span>{{ healthData.strm_tasks.total_tasks }}</span>
                  </div>
                  <div class="flex justify-between">
                    <span>运行中:</span>
                    <span>{{ healthData.strm_tasks.running_tasks }}</span>
                  </div>
                  <div class="flex justify-between">
                    <span>成功率:</span>
                    <span>{{ (healthData.strm_tasks.success_rate * 100).toFixed(1) }}%</span>
                  </div>
                </div>
              </NCard>
            </NGridItem>

            <!-- API性能状态 -->
            <NGridItem>
              <NCard size="small" title="API性能">
                <div class="space-y-2">
                  <div class="flex justify-between">
                    <span>状态:</span>
                    <NTag :type="getStatusColor(healthData.api_performance.status)">
                      {{ healthData.api_performance.status === 'healthy' ? '健康' :
                         healthData.api_performance.status === 'warning' ? '警告' : '错误' }}
                    </NTag>
                  </div>
                  <div class="flex justify-between">
                    <span>平均响应:</span>
                    <span>{{ healthData.api_performance.avg_response_time.toFixed(2) }}s</span>
                  </div>
                  <div class="flex justify-between">
                    <span>24h请求:</span>
                    <span>{{ healthData.api_performance.request_count_24h }}</span>
                  </div>
                  <div class="flex justify-between">
                    <span>错误率:</span>
                    <span>{{ (healthData.api_performance.error_rate * 100).toFixed(2) }}%</span>
                  </div>
                </div>
              </NCard>
            </NGridItem>
          </NGrid>
        </div>
      </div>

      <div v-else-if="!loading" class="text-center text-gray-500 py-8">
        暂无健康状态数据
      </div>
    </NSpin>
  </NCard>
</template>

<style scoped>
.space-y-6 > * + * {
  margin-top: 1.5rem;
}

.space-y-2 > * + * {
  margin-top: 0.5rem;
}

.space-x-2 > * + * {
  margin-left: 0.5rem;
}

.h-full {
  height: 100%;
}
</style>
