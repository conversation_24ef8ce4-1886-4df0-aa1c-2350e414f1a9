<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import { NGrid, NGridItem, NButton, NSpace, NSpin, NAlert } from 'naive-ui';
import AppHealthOverview from './modules/app-health-overview.vue';
import BusinessStats from './modules/business-stats.vue';
import PerformanceCharts from './modules/performance-charts.vue';

// 监控数据状态
const loading = ref(false);
const error = ref<string | null>(null);

// 组件引用
const appHealthRef = ref();
const businessStatsRef = ref();
const performanceChartsRef = ref();

// 自动刷新定时器
let refreshTimer: NodeJS.Timeout | null = null;

onMounted(async () => {
  try {
    // 启动自动刷新（每30秒）
    startAutoRefresh();
  } catch (err) {
    error.value = err instanceof Error ? err.message : '初始化监控页面失败';
  }
});

onUnmounted(() => {
  // 清理定时器
  if (refreshTimer) {
    clearInterval(refreshTimer);
    refreshTimer = null;
  }
});

function startAutoRefresh() {
  // 每30秒自动刷新一次
  refreshTimer = setInterval(() => {
    handleRefresh();
  }, 30000);
}

function handleRefresh() {
  // 触发所有组件刷新
  if (appHealthRef.value?.loadHealthData) {
    appHealthRef.value.loadHealthData();
  }
  if (businessStatsRef.value?.loadBusinessData) {
    businessStatsRef.value.loadBusinessData();
  }
  if (performanceChartsRef.value?.loadPerformanceData) {
    performanceChartsRef.value.loadPerformanceData();
  }
}
</script>

<template>
  <div class="monitor-dashboard">
    <!-- 页面头部 -->
    <div class="flex justify-between items-center mb-6">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">系统监控</h1>
        <p class="text-gray-600 mt-1">实时监控应用健康状态、性能指标和业务数据</p>
      </div>
      <NSpace>
        <NButton @click="handleRefresh" :loading="loading">
          刷新数据
        </NButton>
      </NSpace>
    </div>

    <NSpin :show="loading">
      <div v-if="error" class="mb-4">
        <NAlert type="error" :title="error" closable @close="error = null" />
      </div>

      <NGrid :cols="24" :x-gap="16" :y-gap="16">
        <!-- 应用健康状态概览 -->
        <NGridItem :span="24">
          <AppHealthOverview ref="appHealthRef" />
        </NGridItem>

        <!-- 业务统计 -->
        <NGridItem :span="12">
          <BusinessStats ref="businessStatsRef" />
        </NGridItem>

        <!-- 性能监控 -->
        <NGridItem :span="12">
          <PerformanceCharts ref="performanceChartsRef" />
        </NGridItem>

        <!-- TODO: 后续添加更多监控组件 -->
        <!-- STRM任务监控 -->
        <!-- <NGridItem :span="12">
          <StrmTaskMonitor />
        </NGridItem> -->

        <!-- 用户活动监控 -->
        <!-- <NGridItem :span="12">
          <UserActivityMonitor />
        </NGridItem> -->

        <!-- 错误分析 -->
        <!-- <NGridItem :span="24">
          <ErrorAnalysis />
        </NGridItem> -->
      </NGrid>
    </NSpin>

    <!-- 自动刷新提示 -->
    <div class="fixed bottom-4 right-4 text-xs text-gray-500 bg-white px-3 py-2 rounded-lg shadow-sm border">
      自动刷新: 每30秒
    </div>
  </div>
</template>

<style scoped>
.monitor-dashboard {
  padding: 16px;
  min-height: calc(100vh - 64px);
}

.h-full {
  height: 100%;
}
</style>
